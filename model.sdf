<?xml version="1.0"?>
<sdf version="1.6">
    <model name="x3_uav">
        <pose>0 0 0.053302 0 0 0</pose>
        <link name="base_link">
            <pose frame="">0 0 0 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>1.5</mass>
                <inertia>
                  <ixx>0.025</ixx>
                  <ixy>0</ixy>
                  <ixz>0</ixz>
                  <iyy>0.009</iyy>
                  <iyz>0</iyz>
                  <izz>0.033</izz>
                </inertia>
            </inertial>
            <collision name="base_link_inertia_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <box>
                        <size>0.47 0.47 0.11</size>
                    </box>
                </geometry>
            </collision>
            <visual name="base_link_inertia_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://m_meshes/midas.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Grey</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <sensor name="air_pressure_sensor" type="air_pressure">
                <always_on>1</always_on>
                <update_rate>50</update_rate>
                <air_pressure>
                <pressure>
                    <noise type="gaussian">
                    <mean>0</mean>
                    <stddev>0.01</stddev>
                    </noise>
                </pressure>
                </air_pressure>
            </sensor>
            <sensor name="imu_sensor" type="imu">
                <always_on>1</always_on>
                <update_rate>250</update_rate>
                <imu>
                    <enable_orientation>0</enable_orientation>
                    <angular_velocity>
                        <x>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </x>
                        <y>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </y>
                        <z>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </z>
                    </angular_velocity>
                    <linear_acceleration>
                        <x>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </x>
                        <y>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </y>
                        <z>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </z>
                    </linear_acceleration>
                </imu>

            </sensor>
            <sensor name="air_pressure" type="air_pressure">
                <always_on>1</always_on>
                <update_rate>20</update_rate>
                <air_pressure>
                    <reference_altitude>0</reference_altitude>
                    <noise type="gaussian">
                        <mean>0.00000008</mean>
                    </noise>
                </air_pressure>
            </sensor>
            <sensor name="magnetometer" type="magnetometer">
                <always_on>1</always_on>
                <update_rate>20</update_rate>
                <magnetometer>
                    <x>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </x>
                    <y>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </y>
                    <z>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </z>
                </magnetometer>
            </sensor>

           
        </link>

        <!-- Left Tilt Rotor Assembly -->
        <link name="tilt_rotor_1">
            <pose frame="">-0.4 0 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.15</mass>
                <inertia>
                    <ixx>0.002</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>0.002</iyy>
                    <iyz>0</iyz>
                    <izz>0.001</izz>
                </inertia>
            </inertial>
            <visual name="tilt_rotor_1_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://m_meshes/tilt_rotor_1.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/DarkGrey</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Left Tilt Joint -->
        <joint name="tilt_joint_1" type="revolute">
            <child>tilt_rotor_1</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 1 0</xyz>
                <limit>
                    <lower>-1.57</lower>
                    <upper>1.57</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Left Front Propeller (CCW) -->
        <link name="propeller_1_front">
            <pose frame="">-0.4 0.15 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <visual name="propeller_1_front_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.15 0.15 0.15</scale>
                        <uri>model://m_meshes/propeller_ccw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Left Front Propeller Joint -->
        <joint name="propeller_joint_1_front" type="revolute">
            <child>propeller_1_front</child>
            <parent>tilt_rotor_1</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Left Rear Propeller (CW) -->
        <link name="propeller_1_rear">
            <pose frame="">-0.4 -0.15 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <visual name="propeller_1_rear_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.15 0.15 0.15</scale>
                        <uri>model://m_meshes/propeller_cw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Red</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Left Rear Propeller Joint -->
        <joint name="propeller_joint_1_rear" type="revolute">
            <child>propeller_1_rear</child>
            <parent>tilt_rotor_1</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Right Tilt Rotor Assembly -->
        <link name="tilt_rotor_2">
            <pose frame="">0.4 0 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.15</mass>
                <inertia>
                    <ixx>0.002</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>0.002</iyy>
                    <iyz>0</iyz>
                    <izz>0.001</izz>
                </inertia>
            </inertial>
            <visual name="tilt_rotor_2_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://m_meshes/tilt_rotor_2.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/DarkGrey</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Right Tilt Joint -->
        <joint name="tilt_joint_2" type="revolute">
            <child>tilt_rotor_2</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 1 0</xyz>
                <limit>
                    <lower>-1.57</lower>
                    <upper>1.57</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Right Front Propeller (CW) -->
        <link name="propeller_2_front">
            <pose frame="">0.4 0.15 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <visual name="propeller_2_front_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.15 0.15 0.15</scale>
                        <uri>model://m_meshes/propeller_cw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Green</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Right Front Propeller Joint -->
        <joint name="propeller_joint_2_front" type="revolute">
            <child>propeller_2_front</child>
            <parent>tilt_rotor_2</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Right Rear Propeller (CCW) -->
        <link name="propeller_2_rear">
            <pose frame="">0.4 -0.15 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <visual name="propeller_2_rear_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.15 0.15 0.15</scale>
                        <uri>model://m_meshes/propeller_ccw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Yellow</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Right Rear Propeller Joint -->
        <joint name="propeller_joint_2_rear" type="revolute">
            <child>propeller_2_rear</child>
            <parent>tilt_rotor_2</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Motor Plugins for Tilt Rotors -->
        <!-- Left Front Propeller (CCW) -->
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>propeller_joint_1_front</jointName>
            <linkName>propeller_1_front</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>0</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>

        <!-- Left Rear Propeller (CW) -->
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>propeller_joint_1_rear</jointName>
            <linkName>propeller_1_rear</linkName>
            <turningDirection>cw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>1</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>

        <!-- Right Front Propeller (CW) -->
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>propeller_joint_2_front</jointName>
            <linkName>propeller_2_front</linkName>
            <turningDirection>cw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>2</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>

        <!-- Right Rear Propeller (CCW) -->
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>propeller_joint_2_rear</jointName>
            <linkName>propeller_2_rear</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>3</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
    </model>
</sdf>
