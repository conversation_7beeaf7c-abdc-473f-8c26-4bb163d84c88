<?xml version="1.0"?>
<sdf version="1.6">
    <model name="tiltrotor_drone">
        <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
        <link name="base_link">
            <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
            <inertial>
                <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
                <mass>1.0</mass>
                <inertia>
                  <ixx>0.025</ixx>
                  <ixy>0</ixy>
                  <ixz>0</ixz>
                  <iyy>0.009</iyy>
                  <iyz>0</iyz>
                  <izz>0.033</izz>
                </inertia>
            </inertial>
            <collision name="base_link_inertia_collision">
                <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
                <geometry>
                    <box>
                        <size>0.47 0.47 0.11</size>
                    </box>
                </geometry>
            </collision>
            <visual name="base_link_inertia_visual">
                <pose frame="">0 0 0 -1.5 1.5 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.3 0.3 0.3</scale>
                        <uri>model://drone_model/meshes/body.dae</uri>
                    </mesh>
                </geometry>
            </visual>
            <sensor name="air_pressure_sensor" type="air_pressure">
                <always_on>1</always_on>
                <update_rate>50</update_rate>
                <air_pressure>
                <pressure>
                    <noise type="gaussian">
                    <mean>0</mean>
                    <stddev>0.01</stddev>
                    </noise>
                </pressure>
                </air_pressure>
            </sensor>
            <sensor name="imu_sensor" type="imu">
                <always_on>1</always_on>
                <update_rate>250</update_rate>
                <imu>
                    <enable_orientation>0</enable_orientation>
                    <angular_velocity>
                        <x>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </x>
                        <y>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </y>
                        <z>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </z>
                    </angular_velocity>
                    <linear_acceleration>
                        <x>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </x>
                        <y>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </y>
                        <z>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </z>
                    </linear_acceleration>
                </imu>

            </sensor>
            <sensor name="air_pressure" type="air_pressure">
                <always_on>1</always_on>
                <update_rate>20</update_rate>
                <air_pressure>
                    <reference_altitude>0</reference_altitude>
                    <noise type="gaussian">
                        <mean>0.00000008</mean>
                    </noise>
                </air_pressure>
            </sensor>
            <sensor name="magnetometer" type="magnetometer">
                <always_on>1</always_on>
                <update_rate>20</update_rate>
                <magnetometer>
                    <x>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </x>
                    <y>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </y>
                    <z>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </z>
                </magnetometer>
            </sensor>

           
        </link>
        <link name="rotor_0">
            <pose frame="">0.13 -0.22 0.023 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="rotor_0_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="rotor_0_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.1 0.1 0.1</scale>
                        <uri>model://drone_model/meshes/propeller_ccw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>0 0 1 1</diffuse>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="rotor_0_joint" type="revolute">
            <child>rotor_0</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>
        <link name="rotor_1">
            <pose frame="">-0.13 0.2 0.023 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="rotor_1_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="rotor_1_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.1 0.1 0.1</scale>
                        <uri>model://drone_model/meshes/propeller_ccw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>1 0 0 1</diffuse>
                    <script>
                        <name>Gazebo/Red</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="rotor_1_joint" type="revolute">
            <child>rotor_1</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>
        <link name="rotor_2">
            <pose frame="">0.13 0.22 0.023 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="rotor_2_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="rotor_2_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.1 0.1 0.1</scale>
                        <uri>model://drone_model/meshes/propeller_cw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>0 0 1 1</diffuse>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="rotor_2_joint" type="revolute">
            <child>rotor_2</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>
        <link name="rotor_3">
            <pose frame="">-0.13 -0.2 0.023 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="rotor_3_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="rotor_3_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>0.1 0.1 0.1</scale>
                        <uri>model://drone_model/meshes/propeller_cw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>1 0 0 1</diffuse>
                    <script>
                        <name>Gazebo/Red</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="rotor_3_joint" type="revolute">
            <child>rotor_3</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <plugin filename="ignition-gazebo-multicopter-motor-model-system" name="ignition::gazebo::systems::MulticopterMotorModel">
            <jointName>rotor_0_joint</jointName>
            <linkName>rotor_0</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>0</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>rotor_1_joint</jointName>
            <linkName>rotor_1</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>1</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
            <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>rotor_2_joint</jointName>
            <linkName>rotor_2</linkName>
            <turningDirection>cw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>2</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>rotor_3_joint</jointName>
            <linkName>rotor_3</linkName>
            <turningDirection>cw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>3</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
    </model>
</sdf>
